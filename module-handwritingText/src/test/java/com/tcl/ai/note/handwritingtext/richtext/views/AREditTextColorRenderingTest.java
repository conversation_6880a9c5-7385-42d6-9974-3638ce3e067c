package com.tcl.ai.note.handwritingtext.richtext.views;

import android.content.Context;
import android.graphics.Color;
import android.text.Editable;
import android.text.SpannableStringBuilder;
import android.text.style.ForegroundColorSpan;

import androidx.test.core.app.ApplicationProvider;

import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_FontColor;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import static org.junit.Assert.*;

/**
 * 测试AREditText中字体颜色渲染问题的修复
 * 
 * 测试场景：
 * 1. 在有颜色的文本中间设置新颜色
 * 2. 输入一段会自动换行的长文本
 * 3. 验证所有文本都应用了正确的颜色
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class AREditTextColorRenderingTest {

    private AREditText mEditText;
    private ARE_FontColor mFontColorStyle;
    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        mEditText = new AREditText(mContext);
        mFontColorStyle = new ARE_FontColor();
        
        // 注册字体颜色样式
        mEditText.setInStylesList(mFontColorStyle);
    }

    @Test
    public void testColorRenderingForMultiLineInput() {
        // 1. 设置初始文本，包含一些有颜色的文字
        String initialText = "这是一段有颜色的文字";
        SpannableStringBuilder builder = new SpannableStringBuilder(initialText);
        
        // 给初始文字设置红色
        ForegroundColorSpan redSpan = new ForegroundColorSpan(Color.RED);
        builder.setSpan(redSpan, 0, initialText.length(), SpannableStringBuilder.SPAN_INCLUSIVE_INCLUSIVE);
        
        mEditText.setText(builder);
        
        // 2. 将光标移动到文字中间
        int cursorPosition = initialText.length() / 2;
        mEditText.setSelection(cursorPosition);
        
        // 3. 设置新的颜色（蓝色）
        mFontColorStyle.setFontColor(Color.BLUE);
        
        // 4. 模拟输入一段会换行的长文本
        String longText = "这是一段很长的文字，应该会自动换行显示，我们需要确保所有的文字都显示正确的蓝色，而不是只有最后一行显示蓝色，前面的行显示默认颜色。";
        
        // 模拟文本输入
        Editable editable = mEditText.getEditableText();
        editable.insert(cursorPosition, longText);
        
        // 5. 验证新输入的文字都应用了蓝色
        int startPos = cursorPosition;
        int endPos = cursorPosition + longText.length();
        
        // 检查新输入文字范围内的每个字符是否都有蓝色span
        for (int i = startPos; i < endPos; i++) {
            ForegroundColorSpan[] spans = editable.getSpans(i, i + 1, ForegroundColorSpan.class);
            
            boolean hasBlueSpan = false;
            for (ForegroundColorSpan span : spans) {
                if (span.getForegroundColor() == Color.BLUE) {
                    hasBlueSpan = true;
                    break;
                }
            }
            
            assertTrue("Position " + i + " should have blue color span", hasBlueSpan);
        }
    }

    @Test
    public void testColorStyleActivation() {
        // 测试颜色样式的激活状态
        mFontColorStyle.setFontColor(Color.GREEN);
        
        assertTrue("Font color style should be checked after setting color", 
                   mFontColorStyle.getIsChecked());
    }

    @Test
    public void testColorApplicationToSelection() {
        // 测试对选中文本应用颜色
        String text = "测试文本";
        mEditText.setText(text);
        
        // 选中所有文本
        mEditText.setSelection(0, text.length());
        
        // 应用颜色
        mFontColorStyle.setFontColor(Color.YELLOW);
        
        // 验证所有文本都有黄色span
        Editable editable = mEditText.getEditableText();
        for (int i = 0; i < text.length(); i++) {
            ForegroundColorSpan[] spans = editable.getSpans(i, i + 1, ForegroundColorSpan.class);
            
            boolean hasYellowSpan = false;
            for (ForegroundColorSpan span : spans) {
                if (span.getForegroundColor() == Color.YELLOW) {
                    hasYellowSpan = true;
                    break;
                }
            }
            
            assertTrue("Position " + i + " should have yellow color span", hasYellowSpan);
        }
    }

    @Test
    public void testColorInheritanceForNewInput() {
        // 测试新输入文字继承当前激活的颜色
        
        // 1. 设置颜色样式为激活状态
        mFontColorStyle.setFontColor(Color.MAGENTA);
        
        // 2. 模拟输入新文字
        String newText = "新输入的文字";
        Editable editable = mEditText.getEditableText();
        int startPos = editable.length();
        editable.append(newText);
        int endPos = editable.length();
        
        // 3. 手动触发样式应用（模拟afterTextChanged中的逻辑）
        // 这里我们直接调用applyStyle方法来模拟自动应用
        mFontColorStyle.applyStyle(editable, startPos, endPos, false);
        
        // 4. 验证新输入的文字有正确的颜色
        for (int i = startPos; i < endPos; i++) {
            ForegroundColorSpan[] spans = editable.getSpans(i, i + 1, ForegroundColorSpan.class);
            
            boolean hasMagentaSpan = false;
            for (ForegroundColorSpan span : spans) {
                if (span.getForegroundColor() == Color.MAGENTA) {
                    hasMagentaSpan = true;
                    break;
                }
            }
            
            assertTrue("Position " + i + " should have magenta color span", hasMagentaSpan);
        }
    }
}
